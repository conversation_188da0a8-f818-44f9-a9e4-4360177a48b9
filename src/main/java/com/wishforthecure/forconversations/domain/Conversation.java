package com.wishforthecure.forconversations.domain;

import com.wishforthecure.forconversations.domain.enumeration.Feeling;
import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@Document(collection = "conversation")
@org.springframework.data.elasticsearch.annotations.Document(indexName = "conversation")
public class Conversation extends AbstractAuditingEntity<String> implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    private String id;

    @Field("name")
    private String name;

    @Field("start")
    private Instant start;

    @Field("end")
    private Instant end;

    @Field("message_list")
    private List<Message> messageList;

    @Field("context")
    private String context;

    @Field("feeling_list")
    private List<Feeling> feelingList;

    @Field("tag_list")
    private List<Tag> tagList;

    @Field("user_id")
    private String userId;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public String getId() {
        return this.id;
    }

    public Conversation id(String id) {
        this.setId(id);
        return this;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public Conversation name(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Instant getStart() {
        return this.start;
    }

    public Conversation start(Instant start) {
        this.setStart(start);
        return this;
    }

    public void setStart(Instant start) {
        this.start = start;
    }

    public Instant getEnd() {
        return this.end;
    }

    public Conversation end(Instant end) {
        this.setEnd(end);
        return this;
    }

    public void setEnd(Instant end) {
        this.end = end;
    }

    public List<Message> getMessageList() {
        return this.messageList;
    }

    public Conversation messageList(List<Message> messageList) {
        this.setMessageList(messageList);
        return this;
    }

    public void setMessageList(List<Message> messageList) {
        this.messageList = messageList;
    }

    public String getContext() {
        return this.context;
    }

    public Conversation context(String context) {
        this.setContext(context);
        return this;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public List<Feeling> getFeelingList() {
        return this.feelingList;
    }

    public Conversation feelingList(List<Feeling> feelingList) {
        this.setFeelingList(feelingList);
        return this;
    }

    public void setFeelingList(List<Feeling> feelingList) {
        this.feelingList = feelingList;
    }

    public List<Tag> getTagList() {
        return this.tagList;
    }

    public Conversation tagList(List<Tag> tagList) {
        this.setTagList(tagList);
        return this;
    }

    public void setTagList(List<Tag> tagList) {
        this.tagList = tagList;
    }

    public String getUserId() {
        return this.userId;
    }

    public Conversation userId(String userId) {
        this.setUserId(userId);
        return this;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Conversation)) {
            return false;
        }
        return getId() != null && getId().equals(((Conversation) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    public void addTag(com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain tagDomain) {
        // Implementation needed
    }

    public void assignFeeling(com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain feeling) {
        // Implementation needed
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Conversation{" +
            "id=" + getId() +
            ", name='" + getName() + "'" +
            ", start='" + getStart() + "'" +
            ", end='" + getEnd() + "'" +
            ", context='" + getContext() + "'" +
            "}";
    }
}
