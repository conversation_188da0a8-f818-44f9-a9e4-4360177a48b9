package com.wishforthecure.forconversations.hexa.application.dto;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.Set;
import lombok.Value;

@Value
public class ConversationDTO {

    ConversationDomainId conversationDomainId;
    String name;
    Instant startDate;
    Instant endDate;
    FeelingDomain feeling;
    Set<TagDomain> tags;
}
