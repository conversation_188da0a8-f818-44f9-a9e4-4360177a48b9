package com.wishforthecure.forconversations.hexa.application.port.in;

import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;

import reactor.core.publisher.Mono;

public interface AssignFeelingUseCase {

    Mono<Void> assignFeeling(ConversationDomainId conversationDomainId, FeelingDomain feeling);
}
