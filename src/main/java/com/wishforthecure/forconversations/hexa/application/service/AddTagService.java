package com.wishforthecure.forconversations.hexa.application.service;

import org.springframework.stereotype.Service;

import com.wishforthecure.forconversations.hexa.application.port.in.AddTagUseCase;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import com.wishforthecure.forconversations.repository.ConversationRepository;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class AddTagService implements AddTagUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Mono<Void> addTag(ConversationDomainId conversationDomainId, String tagName) {
        return conversationRepository.findById(conversationDomainId)
                .switchIfEmpty(Mono.error(new IllegalArgumentException("Conversation not found")))
                .map(conversation -> {
                    conversation.addTag(TagDomain.of(tagName));
                    return conversation;
                })
                .flatMap(conversationRepository::save)
                .then();
    }

}
