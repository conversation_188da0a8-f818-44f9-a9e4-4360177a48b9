package com.wishforthecure.forconversations.hexa.application.service;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.wishforthecure.forconversations.hexa.application.port.in.EmailSourcePort;
import com.wishforthecure.forconversations.hexa.application.port.out.EmailSourceRepository;
import com.wishforthecure.forconversations.hexa.application.utils.MBoxUtils;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.EmailSourceDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceDomainId;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceSaveDTO;
import com.wishforthecure.forconversations.hexa.infrastructure.adapter.in.controller.dto.EmailSourceUploadDTO;

import lombok.RequiredArgsConstructor;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class EmailSourceService implements EmailSourcePort {

    private static final Logger LOG = LoggerFactory.getLogger(EmailSourceService.class);

    private final EmailSourceRepository emailSourceRepository;
    private final MBoxUtils mboxUtils;

    @Override
    public Mono<EmailSourceSaveDTO> upload(EmailSourceUploadDTO emailSourceUploadDTO) {
        byte[] bytes = emailSourceUploadDTO.getFile();

        List<EmailMessageDomain> messages = mboxUtils.parse(bytes);

        LOG.debug("Parsed {} email messages from MBOX file", messages.size());

        EmailSourceDomain source = new EmailSourceDomain(new SourceDomainId(UUID.randomUUID()), Instant.now(), messages,
                bytes);

        return emailSourceRepository
                .save(source)
                .map(savedSource -> new EmailSourceSaveDTO(
                        savedSource.getSourceId().getValue().toString(),
                        savedSource.getEmailMessageDomainList().size()));
    }

    @Override
    public Mono<EmailSourceSaveDTO> save(EmailSourceSaveDTO emailSourceSaveDTO) {
        throw new UnsupportedOperationException("Unimplemented method 'save'");
    }
}

path:src/application/service/WhatsAppSourceService.java
