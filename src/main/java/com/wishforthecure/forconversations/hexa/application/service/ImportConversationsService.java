package com.wishforthecure.forconversations.hexa.application.service;

import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.UUID;

import org.springframework.stereotype.Service;

import com.wishforthecure.forconversations.hexa.application.port.in.ImportConversationsUseCase;
import com.wishforthecure.forconversations.hexa.application.utils.MBoxUtils;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.Conversation;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.repository.ConversationRepository;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ImportConversationsService implements ImportConversationsUseCase {

    private final MBoxUtils mboxUtils;
    private final ConversationRepository conversationRepository;

    @Override
    public void importFromMbox(byte[] mboxFileBytes) {
        List<EmailMessageDomain> messages = mboxUtils.parse(mboxFileBytes);

        if (!messages.isEmpty()) {
            Conversation conversation = new Conversation(
                    new ConversationDomainId(UUID.randomUUID()),
                    messages.get(0).getContent().split("##_##")[0],
                    messages.stream().map(Message::getTime).min(Instant::compareTo).orElse(Instant.now()),
                    messages.stream().map(Message::getTime).max(Instant::compareTo).orElse(Instant.now()),
                    null,
                    new HashSet<>(),
                    (List) messages);
            conversationRepository.save(conversation).block();
        }
    }
}