package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.dto.ConversationDTO;
import com.wishforthecure.forconversations.hexa.application.port.in.ListConversationsUseCase;
import com.wishforthecure.forconversations.hexa.application.port.out.ConversationRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;

@Service
@RequiredArgsConstructor
public class ListConversationsService implements ListConversationsUseCase {

    private final ConversationRepository conversationRepository;

    @Override
    public Flux<ConversationDTO> listAll() {
        return conversationRepository.findAll()
                .map(conversation -> new ConversationDTO(
                        conversation.getId(),
                        conversation.getName(),
                        conversation.getStartDate(),
                        conversation.getEndDate(),
                        conversation.getFeeling(),
                        conversation.getTags()
                ));
    }
}

path: src/application/service/RenameConversationService.java
