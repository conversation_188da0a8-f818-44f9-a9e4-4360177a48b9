package com.wishforthecure.forconversations.hexa.application.service;

import com.wishforthecure.forconversations.hexa.application.port.in.SourcePort;
import com.wishforthecure.forconversations.hexa.application.port.out.SourceRepository;
import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.service.dto.SourceDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
@RequiredArgsConstructor
public class SourceService implements SourcePort {

    private final SourceRepository sourceRepository;

    @Override
    public Mono<SourceDTO> save(SourceDTO sourceDTO) {
        Source source = mapToDomain(sourceDTO);
        return sourceRepository.save(source)
                .map(this::mapToDTO);
    }

    private Source mapToDomain(SourceDTO dto) {
        throw new UnsupportedOperationException("Mapping not implemented");
    }

    private SourceDTO mapToDTO(Source source) {
        throw new UnsupportedOperationException("Mapping not implemented");
    }
}

path: src/application/service/RemoveTagService.java
