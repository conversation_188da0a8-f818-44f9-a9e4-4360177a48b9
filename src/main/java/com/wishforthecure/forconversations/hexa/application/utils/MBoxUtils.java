package com.wishforthecure.forconversations.hexa.application.utils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
    import java.util.Properties;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.james.mime4j.mboxiterator.CharBufferWrapper;
import org.apache.james.mime4j.mboxiterator.MboxIterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.MessageId;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;

import jakarta.mail.Address;
import jakarta.mail.Message;
import jakarta.mail.MessagingException;
import jakarta.mail.Multipart;
import jakarta.mail.Part;
import jakarta.mail.Session;
import jakarta.mail.internet.MimeMessage;

@Component
public class MBoxUtils {

    private static final Logger LOG = LoggerFactory.getLogger(MBoxUtils.class);

    public List<EmailMessageDomain> parse(byte[] mboxBytes) {
        List<EmailMessageDomain> emailList = new ArrayList<>();
        final Session session = Session.getDefaultInstance(new Properties());

        if (mboxBytes == null || mboxBytes.length == 0) {
            LOG.debug("Empty or null MBOX content provided");
            return emailList;
        }

        LOG.debug("Processing MBOX file with {} bytes", mboxBytes.length);

        String preview = new String(mboxBytes, StandardCharsets.UTF_8);
        String[] lines = preview.split("\n", 5);
        LOG.debug("First few lines of MBOX:");
        for (int i = 0; i < Math.min(lines.length, 3); i++) {
            LOG.debug("Line {}: '{}'", i + 1, lines[i]);
        }

        Path tempFile = null;
        try {
            tempFile = Files.createTempFile("mbox", ".tmp");
            try (OutputStream os = Files.newOutputStream(tempFile)) {
                os.write(mboxBytes);
            }

            LOG.debug("Created temporary file: {}", tempFile.toAbsolutePath());

            int messageCount = 0;
            for (final CharBufferWrapper messageCharBuffer : MboxIterator.fromFile(tempFile.toFile())
                    .charset(StandardCharsets.UTF_8)
                    .build()) {
                messageCount++;
                LOG.debug("Processing message #{}", messageCount);

                EmailMessageDomain dto = processMessage(messageCharBuffer, session);
                if (dto != null) {
                    emailList.add(dto);
                }
            }

            LOG.info("Successfully parsed {} messages from MBOX file", emailList.size());
        } catch (IllegalArgumentException e) {
            LOG.warn("Invalid MBOX format detected: {}", e.getMessage());
            LOG.debug("MBOX content preview (first 500 chars): {}",
                    preview.length() > 500 ? preview.substring(0, 500) + "..." : preview);
        } catch (IOException e) {
            LOG.error("Error al leer el stream del MBOX: {}", e.getMessage(), e);
        } finally {
            if (tempFile != null && Files.exists(tempFile)) {
                try {
                    Files.delete(tempFile);
                } catch (IOException e) {
                    LOG.warn("Failed to delete temporary file: {}", tempFile.toAbsolutePath(), e);
                }
            }
        }

        return emailList;
    }

    private EmailMessageDomain processMessage(CharBufferWrapper messageCharBuffer, Session session) {
        try {
            String messageContent = messageCharBuffer.toString();
            InputStream messageInputStream = new ByteArrayInputStream(messageContent.getBytes(StandardCharsets.UTF_8));
            MimeMessage message = new MimeMessage(session, messageInputStream);

            String messageIdStr = message.getMessageID();
            MessageId messageId = new MessageId(
                    messageIdStr != null ? UUID.nameUUIDFromBytes(messageIdStr.getBytes()) : UUID.randomUUID());

            Date sentDate = message.getSentDate();
            Instant time = sentDate != null ? sentDate.toInstant() : Instant.now();

            Address[] fromAddresses = message.getFrom();
            String senderStr = fromAddresses != null && fromAddresses.length > 0 ? fromAddresses[0].toString() : "";
            EmailAliasDomain sender = new EmailAliasDomain(null, null, senderStr, null);

            List<EmailAliasDomain> recipients = getAllRecipients(message);

            String subject = message.getSubject() != null ? message.getSubject() : "";
            String body = getTextFromMessage(message);
            String content = subject + "##_##" + body;

            return new EmailMessageDomain(messageId, time, sender, recipients, content,
                    new ArrayList<>(), new ArrayList<>(), SourceType.EMAIL);
        } catch (MessagingException | IOException e) {
            LOG.warn("Error procesando un mensaje, se saltará: {}", e.getMessage(), e);
            return null;
        }
    }

    private String getTextFromMessage(Message message) throws MessagingException, IOException {
        if (message.isMimeType("text/plain")) {
            return message.getContent().toString();
        }
        if (message.isMimeType("multipart/*")) {
            return getTextFromMultipart((Multipart) message.getContent());
        }
        return "";
    }

    private String getTextFromMultipart(Multipart multipart) throws MessagingException, IOException {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < multipart.getCount(); i++) {
            Part bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("text/plain")) {
                return bodyPart.getContent().toString();
            } else if (bodyPart.isMimeType("text/html")) {
            } else if (bodyPart.getContent() instanceof Multipart nestedMultipart) {
                String nestedText = getTextFromMultipart(nestedMultipart);
                if (!nestedText.isEmpty()) {
                    return nestedText;
                }
            }
        }
        return result.toString();
    }

    private List<EmailAliasDomain> getAllRecipients(Message message) throws MessagingException {
        Stream<Address> to = safeStream(message.getRecipients(Message.RecipientType.TO));
        Stream<Address> cc = safeStream(message.getRecipients(Message.RecipientType.CC));
        Stream<Address> bcc = safeStream(message.getRecipients(Message.RecipientType.BCC));

        return Stream.concat(to, Stream.concat(cc, bcc))
                .map(addr -> new EmailAliasDomain(null, null, null, addr.toString()))
                .collect(Collectors.toList());
    }

    private Stream<Address> safeStream(Address[] addresses) {
        return addresses == null ? Stream.empty() : Arrays.stream(addresses);
    }
}

path: src/application/service/ImportConversationsService.java
