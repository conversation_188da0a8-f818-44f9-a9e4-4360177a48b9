package com.wishforthecure.forconversations.hexa.domain.model.alias;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Getter
@AllArgsConstructor
public class EmailRecipientAliasDomain implements AliasDomain {

    private final String recipientName;
    private final String recipientEmail;

    @Override
    public String getName() {
        return recipientName;
    }

    @Override
    public String getValue() {
        return recipientEmail;
    }
}
