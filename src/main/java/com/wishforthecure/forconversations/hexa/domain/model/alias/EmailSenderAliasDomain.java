package com.wishforthecure.forconversations.hexa.domain.model.alias;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Getter
@AllArgsConstructor
public class EmailSenderAliasDomain implements AliasDomain {

    private final String senderName;
    private final String senderEmail;

    @Override
    public String getName() {
        return senderName;
    }

    @Override
    public String getValue() {
        return senderEmail;
    }
}
