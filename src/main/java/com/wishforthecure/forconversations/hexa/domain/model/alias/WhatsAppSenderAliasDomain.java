package com.wishforthecure.forconversations.hexa.domain.model.alias;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Getter
@AllArgsConstructor
public class WhatsAppSenderAliasDomain implements AliasDomain {

    private final String senderName;
    private final String senderMobile;

    @Override
    public String getName() {
        return senderName;
    }

    @Override
    public String getValue() {
        return senderMobile;
    }
}
