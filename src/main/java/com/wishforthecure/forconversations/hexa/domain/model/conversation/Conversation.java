package com.wishforthecure.forconversations.hexa.domain.model.conversation;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
public class Conversation {

    ConversationDomainId id;
    String name;
    Instant startDate;
    Instant endDate;
    Set<FeelingDomain> feelings;
    Set<TagDomain> tags;
    List<Message> messages;
}
