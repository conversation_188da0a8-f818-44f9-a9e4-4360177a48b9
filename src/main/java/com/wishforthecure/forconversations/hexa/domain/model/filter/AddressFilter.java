package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.util.Set;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class AddressFilter implements MessageFilter {

    Set<EmailAddress> addresses;
    FilterMode mode;

    @Override
    public boolean apply(Message message) {
        String sender = message.getSender();
        String recipients = message.getRecipients();
        boolean matches = addresses.stream().anyMatch(addr -> sender.contains(addr.getValue()) || recipients.contains(addr.getValue()));
        return mode == FilterMode.INCLUDE ? matches : !matches;
    }
}
