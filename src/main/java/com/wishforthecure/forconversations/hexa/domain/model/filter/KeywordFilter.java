package com.wishforthecure.forconversations.hexa.domain.model.filter;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.util.Set;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class KeywordFilter implements MessageFilter {

    Set<String> keywords;
    FilterMode mode;

    @Override
    public boolean apply(Message message) {
        String content = message.getContent();
        boolean matches = keywords.stream().anyMatch(kw -> content.contains(kw));
        return mode == FilterMode.INCLUDE ? matches : !matches;
    }
}
