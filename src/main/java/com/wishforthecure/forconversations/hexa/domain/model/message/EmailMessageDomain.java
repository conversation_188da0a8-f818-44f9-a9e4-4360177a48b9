package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailRecipientAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.alias.EmailSenderAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailMessageDomain implements Message {

    MessageDomainId messageId;
    Instant time;
    EmailSenderAliasDomain sender;
    List<EmailRecipientAliasDomain> recipients;
    String content;
    List<FeelingDomain> feelingDomainList;
    List<TagDomain> tagDomainList;
    SourceType sourceType;

    @Override
    public String getSender() {
        return sender.getName();
    }

    @Override
    public String getRecipients() {
        return recipients.stream().map(EmailRecipientAliasDomain::getName).collect(Collectors.joining(", "));
    }
}
