package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.List;
import org.jmolecules.ddd.annotation.Entity;

@Entity
public interface Message {
    MessageDomainId getMessageId();

    Instant getTime();

    String getSender();

    String getRecipients();

    String getContent();

    List<FeelingDomain> getFeelingDomainList();

    List<TagDomain> getTagDomainList();
}
