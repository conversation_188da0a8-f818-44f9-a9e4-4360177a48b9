package com.wishforthecure.forconversations.hexa.domain.model.message;

import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppRecipientAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.alias.WhatsAppSenderAliasDomain;
import com.wishforthecure.forconversations.hexa.domain.model.feeling.FeelingDomain;
import com.wishforthecure.forconversations.hexa.domain.model.tag.TagDomain;
import java.time.Instant;
import java.util.List;
import lombok.Value;
import org.jmolecules.ddd.annotation.ValueObject;

@ValueObject
@Value
public class WhatsAppAudioMessageDomain implements Message {

    MessageDomainId messageId;
    Instant time;
    WhatsAppSenderAliasDomain sender;
    WhatsAppRecipientAliasDomain recipient;
    String content;
    byte[] file;
    List<FeelingDomain> feelingDomainList;
    List<TagDomain> tagDomainList;

    public String getSender() {
        return sender.getValue();
    }

    public String getRecipients() {
        return recipient.getValue();
    }
}
