package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import java.time.Instant;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jmolecules.ddd.annotation.AggregateRoot;

@AggregateRoot
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailSourceDomain implements Source {

    SourceDomainId sourceId;
    Instant time;
    List<EmailMessageDomain> messages;
    byte[] source;
    SourceType sourceType = SourceType.EMAIL;
}
