package com.wishforthecure.forconversations.hexa.domain.model.source;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.time.Instant;
import java.util.List;
import org.jmolecules.ddd.annotation.Entity;

@Entity
public interface Source {
    SourceDomainId getSourceId();

    Instant getTime();

    List<? extends Message> getMessages();

    byte[] getSource();
}
