package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.filter.AddressFilter;
import com.wishforthecure.forconversations.hexa.domain.model.filter.DateRangeFilter;
import com.wishforthecure.forconversations.hexa.domain.model.filter.FilterMode;
import com.wishforthecure.forconversations.hexa.domain.model.filter.KeywordFilter;
import com.wishforthecure.forconversations.hexa.domain.model.filter.MessageFilter;
import com.wishforthecure.forconversations.hexa.domain.model.message.EmailMessageDomain;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.time.Instant;
import java.util.List;
import java.util.Set;

public class EmailProcessingApplicationService {

    private final MessageFilteringService filteringService;

    public EmailProcessingApplicationService(MessageFilteringService filteringService) {
        this.filteringService = filteringService;
    }

    public List<Message> processAndFilterEmails(List<EmailMessageDomain> incomingEmails) {
        MessageFilter dateFilter = DateRangeFilter.of(
            Instant.parse("2025-01-01T00:00:00Z"),
            Instant.parse("2025-06-30T23:59:59Z"),
            FilterMode.INCLUDE
        );

        MessageFilter keywordFilter = KeywordFilter.of(Set.of("promotion"), FilterMode.EXCLUDE);

        MessageFilter addressFilter = AddressFilter.of(Set.of("<EMAIL>", "<EMAIL>"), FilterMode.EXCLUDE);

        List<MessageFilter> activeFilters = List.of(dateFilter, keywordFilter, addressFilter);

        return filteringService.filterMessages(incomingEmails, activeFilters);
    }
}

path: src/domain/service/SourceParsingService.java
