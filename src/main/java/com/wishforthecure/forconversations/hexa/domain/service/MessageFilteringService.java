package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.filter.MessageFilter;
import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import java.util.List;
import java.util.stream.Collectors;
import org.jmolecules.ddd.annotation.Service;

@Service
public class MessageFilteringService {

    public List<Message> filterMessages(List<? extends Message> messages, List<MessageFilter> filters) {
        if (filters == null || filters.isEmpty()) {
            return List.copyOf(messages);
        }

        return messages.stream().filter(message -> passesAllFilters(message, filters)).collect(Collectors.toList());
    }

    private boolean passesAllFilters(Message message, List<MessageFilter> filters) {
        for (MessageFilter filter : filters) {
            if (!filter.apply(message)) {
                return false;
            }
        }
        return true;
    }
}

path: src/domain/service/EmailProcessingApplicationService.java
