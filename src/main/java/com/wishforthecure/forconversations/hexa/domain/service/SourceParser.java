package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.message.Message;
import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceType;
import java.util.List;

public interface SourceParser {
    List<Message> parse(Source source);

    SourceType getSupportedSourceType();
}

path: src/domain/service/MessageFilteringService.java
