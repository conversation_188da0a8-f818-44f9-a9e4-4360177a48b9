package com.wishforthecure.forconversations.hexa.domain.service;

import com.wishforthecure.forconversations.hexa.domain.model.source.Source;
import com.wishforthecure.forconversations.hexa.domain.model.source.SourceId;
import java.util.Optional;
import org.jmolecules.ddd.annotation.Repository;

@Repository
public interface SourceRepository {
    Source save(Source source);

    Optional<Source> findById(SourceId id);
}
</DOCUMENT>
