package com.wishforthecure.forconversations.repository;

import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;

import com.wishforthecure.forconversations.domain.Conversation;
import com.wishforthecure.forconversations.hexa.domain.model.conversation.ConversationDomainId;

import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Repository
public interface ConversationRepository extends ReactiveMongoRepository<Conversation, String> {
    Flux<Conversation> findByUserId(String userId);

    Flux<Conversation> findByUserIdOrderByStartDesc(String userId);

    Mono<Conversation> findById(ConversationDomainId conversationDomainId);
}
